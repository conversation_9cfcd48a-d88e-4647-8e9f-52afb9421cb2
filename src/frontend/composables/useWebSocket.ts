import { ref, onMounted, onUnmounted } from 'vue'
import { io, Socket } from 'socket.io-client'
import { useToast } from './useToast'
import { useDataRefresh } from './useDataRefresh'
import { useMetrics } from './useMetrics'

interface EmailProcessedData {
  messageId: string
  fromAddress: string
  subject: string
  isTestWebhook: boolean
  deliveryStatus: 'DELIVERED' | 'FAILED'
  webhookPayload?: any
  timestamp: string
}

interface MetricsUpdatedData {
  domains: number
  aliases: number
  webhooks: number
  emails: number
}

// Global WebSocket instance
let globalSocket: Socket | null = null
let connectionCount = 0

export function useWebSocket() {
  const { success: showSuccess, error: showError } = useToast()
  const { triggerRefresh } = useDataRefresh()
  const { refreshMetrics } = useMetrics()
  
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionError = ref<string | null>(null)

  const connect = () => {
    if (globalSocket?.connected) {
      isConnected.value = true
      return
    }

    if (isConnecting.value) return

    isConnecting.value = true
    connectionError.value = null

    try {
      // Use secure cookie-based authentication only
      console.log('Connecting WebSocket with secure cookie authentication...')

      globalSocket = io({
        withCredentials: true, // Send httpOnly cookies automatically
        transports: ['websocket', 'polling'],
        timeout: 10000,
        retries: 3
      })

      globalSocket.on('connect', () => {
        console.log('WebSocket connected')
        isConnected.value = true
        isConnecting.value = false
        connectionError.value = null
      })

      globalSocket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason)
        isConnected.value = false
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          setTimeout(() => connect(), 2000)
        }
      })

      globalSocket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error)
        isConnected.value = false
        isConnecting.value = false
        connectionError.value = error.message
      })

      // Handle email processed events
      globalSocket.on('email_processed', (data: EmailProcessedData) => {
        console.log('Email processed event received:', data)
        
        if (data.isTestWebhook) {
          showSuccess(`Test email processed! From: ${data.fromAddress}`)
          
          // Trigger logs refresh if user is on logs page
          if (window.location.pathname.includes('/logs')) {
            triggerRefresh('aliases') // This will refresh logs
          }
        } else {
          showSuccess(`Email processed and delivered to webhook`)
        }

        // Always refresh metrics for email count
        setTimeout(() => refreshMetrics(), 500)
      })

      // Handle metrics updated events
      globalSocket.on('metrics_updated', (data: MetricsUpdatedData) => {
        console.log('Metrics updated event received:', data)
        
        // Update metrics in the metrics composable
        refreshMetrics()
      })

      // Handle pong responses
      globalSocket.on('pong', () => {
        console.log('WebSocket pong received')
      })

    } catch (error: any) {
      console.error('Failed to initialize WebSocket:', error)
      isConnecting.value = false
      connectionError.value = error.message
    }
  }

  const disconnect = () => {
    if (globalSocket) {
      globalSocket.disconnect()
      globalSocket = null
    }
    isConnected.value = false
    isConnecting.value = false
  }

  const ping = () => {
    if (globalSocket?.connected) {
      globalSocket.emit('ping')
    }
  }

  // Auto-connect on mount, disconnect on unmount
  onMounted(() => {
    connectionCount++
    if (connectionCount === 1) {
      connect()
    } else {
      // Already connected, just update state
      isConnected.value = globalSocket?.connected || false
    }
  })

  onUnmounted(() => {
    connectionCount--
    if (connectionCount === 0) {
      disconnect()
    }
  })

  return {
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    ping
  }
}

// Global WebSocket functions for manual control
export const globalWebSocket = {
  connect: () => {
    const { connect } = useWebSocket()
    connect()
  },
  
  disconnect: () => {
    const { disconnect } = useWebSocket()
    disconnect()
  },
  
  getStatus: () => ({
    connected: globalSocket?.connected || false,
    id: globalSocket?.id
  })
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).webSocket = globalWebSocket
}
